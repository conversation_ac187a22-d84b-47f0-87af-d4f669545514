<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 800px;
            padding: 20px;
            font-family: Arial, sans-serif;
            max-height: 600px;
            overflow-y: auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .import-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .import-format {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .textarea-container {
            margin: 15px 0;
        }
        .textarea-container textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
            box-sizing: border-box;
        }
        .form-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        input[type="text"] {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 12px;
        }
        .button-group {
            margin-top: 20px;
            text-align: center;
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            min-width: 100px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        .warning-text {
            color: #ff9d00;
            font-size: 11px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>🚀 TaxSlayer 杀手半自动填写助手</h3>
    </div>

    <div class="import-section">
        <div class="section-title">📋 批量导入数据</div>
        <div>请按以下格式输入数据(用Tab键分隔):</div>
        <div class="import-format">邮箱	用户名	密码	手机号	手机验证链接	名字	姓氏	社会安全号	出生日期	地址	邮编	雇主EIN	雇主公司	雇主地址	雇主邮编	金额1	金额2	金额3	金额4	金额5	金额6	金额16	金额17	银行名称	路由号码	账号</div>
        <div class="warning-text">注意: 手机验证链接必须完整,例如: https://example.com/verify/123456</div>
        <div class="textarea-container">
            <textarea id="importData" placeholder="在此粘贴数据..."></textarea>
        </div>
        <button id="importBtn" class="btn-success">导入并开始</button>
    </div>

    <div class="section-title">📝 手动输入数据</div>
    <div class="form-section">
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="text" id="email" placeholder="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" placeholder="用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="text" id="password" placeholder="密码">
        </div>
        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="text" id="phone" placeholder="手机号">
        </div>
        <div class="form-group">
            <label for="phoneLink">手机验证链接:</label>
            <input type="text" id="phoneLink" placeholder="验证链接">
        </div>
        <div class="form-group">
            <label for="firstName">名字:</label>
            <input type="text" id="firstName" placeholder="First Name">
        </div>
        <div class="form-group">
            <label for="lastName">姓氏:</label>
            <input type="text" id="lastName" placeholder="Last Name">
        </div>
        <div class="form-group">
            <label for="ssn">SSN:</label>
            <input type="text" id="ssn" placeholder="***********">
        </div>
        <div class="form-group">
            <label for="dob">出生日期:</label>
            <input type="text" id="dob" placeholder="MM/DD/YYYY">
        </div>
        <div class="form-group">
            <label for="address">地址:</label>
            <input type="text" id="address" placeholder="街道地址">
        </div>
        <div class="form-group">
            <label for="zip">邮编:</label>
            <input type="text" id="zip" placeholder="12345">
        </div>
        <div class="form-group">
            <label for="employerEin">雇主EIN:</label>
            <input type="text" id="employerEin" placeholder="12-3456789">
        </div>
        <div class="form-group">
            <label for="employerName">雇主公司:</label>
            <input type="text" id="employerName" placeholder="公司名称">
        </div>
        <div class="form-group">
            <label for="employerAddress">雇主地址:</label>
            <input type="text" id="employerAddress" placeholder="公司地址">
        </div>
        <div class="form-group">
            <label for="employerZip">雇主邮编:</label>
            <input type="text" id="employerZip" placeholder="12345">
        </div>
        <div class="form-group">
            <label for="amount1">金额1:</label>
            <input type="text" id="amount1" placeholder="联邦工资">
        </div>
        <div class="form-group">
            <label for="amount2">金额2:</label>
            <input type="text" id="amount2" placeholder="联邦预扣">
        </div>
        <div class="form-group">
            <label for="amount3">金额3:</label>
            <input type="text" id="amount3" placeholder="社保工资">
        </div>
        <div class="form-group">
            <label for="amount4">金额4:</label>
            <input type="text" id="amount4" placeholder="社保预扣">
        </div>
        <div class="form-group">
            <label for="amount5">金额5:</label>
            <input type="text" id="amount5" placeholder="医保工资">
        </div>
        <div class="form-group">
            <label for="amount6">金额6:</label>
            <input type="text" id="amount6" placeholder="医保预扣">
        </div>
        <div class="form-group">
            <label for="amount16">金额16:</label>
            <input type="text" id="amount16" placeholder="州工资">
        </div>
        <div class="form-group">
            <label for="amount17">金额17:</label>
            <input type="text" id="amount17" placeholder="州税">
        </div>
        <div class="form-group">
            <label for="bankName">银行名称:</label>
            <input type="text" id="bankName" placeholder="银行名称">
        </div>
        <div class="form-group">
            <label for="routingNumber">路由号码:</label>
            <input type="text" id="routingNumber" placeholder="路由号码">
        </div>
        <div class="form-group">
            <label for="accountNumber">账号:</label>
            <input type="text" id="accountNumber" placeholder="账号">
        </div>
    </div>

    <div class="button-group">
        <button id="saveData" class="btn-primary">保存数据</button>
        <button id="loadData" class="btn-info">加载数据</button>
        <button id="clearData" class="btn-warning">清空数据</button>
        <button id="startAuto" class="btn-success">开始自动填写</button>
        <button id="stopAuto" class="btn-danger">停止自动填写</button>
    </div>

    <div id="status" class="status hidden"></div>

    <script src="popup.js"></script>
</body>
</html>
