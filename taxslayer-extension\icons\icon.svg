<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="#4CAF50" stroke="#2E7D32" stroke-width="4"/>
  
  <!-- 计算器主体 -->
  <rect x="32" y="28" width="64" height="72" rx="8" fill="#FFFFFF" stroke="#333" stroke-width="2"/>
  
  <!-- 显示屏 -->
  <rect x="38" y="34" width="52" height="16" rx="2" fill="#1976D2" stroke="#0D47A1" stroke-width="1"/>
  
  <!-- 显示屏文字 -->
  <text x="64" y="46" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TAX</text>
  
  <!-- 按钮行1 -->
  <rect x="40" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="54" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="68" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="82" y="56" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
  
  <!-- 按钮行2 -->
  <rect x="40" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="54" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="68" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="82" y="68" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
  
  <!-- 按钮行3 -->
  <rect x="40" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="54" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="68" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="82" y="80" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
  
  <!-- 按钮行4 -->
  <rect x="40" y="92" width="24" height="8" rx="1" fill="#4CAF50" stroke="#2E7D32" stroke-width="0.5"/>
  <rect x="68" y="92" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
  <rect x="82" y="92" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
  
  <!-- 按钮标签 -->
  <text x="45" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">7</text>
  <text x="59" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">8</text>
  <text x="73" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">9</text>
  <text x="87" y="62" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">÷</text>
  
  <text x="45" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">4</text>
  <text x="59" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">5</text>
  <text x="73" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">6</text>
  <text x="87" y="74" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">×</text>
  
  <text x="45" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">1</text>
  <text x="59" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">2</text>
  <text x="73" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">3</text>
  <text x="87" y="86" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">-</text>
  
  <text x="52" y="98" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">0</text>
  <text x="73" y="98" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">.</text>
  <text x="87" y="98" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">+</text>
  
  <!-- 自动化图标 -->
  <circle cx="100" cy="28" r="12" fill="#FF5722" stroke="#D84315" stroke-width="2"/>
  <text x="100" y="32" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="8" font-weight="bold">A</text>
  
  <!-- 装饰性元素 -->
  <path d="M 20 110 Q 64 105 108 110" stroke="#4CAF50" stroke-width="2" fill="none"/>
  <circle cx="25" cy="108" r="2" fill="#4CAF50"/>
  <circle cx="103" cy="108" r="2" fill="#4CAF50"/>
</svg>
