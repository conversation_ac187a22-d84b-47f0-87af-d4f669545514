# 🚀 杀手半自动插件 - 安装和测试指南

## ⚡ 快速安装步骤

### 1. 获取图标文件
**方法1：使用生成器（推荐）**
- 打开 `icons/generate-icons.html` 文件
- 点击"生成图标"和"下载所有图标"
- 将下载的文件重命名为 `icon16.png`, `icon48.png`, `icon128.png`

**方法2：跳过图标**
- 插件没有图标也能正常工作，只是显示默认图标

### 2. 加载扩展程序
1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `taxslayer-extension` 文件夹
6. 确认加载成功

### 3. 验证安装
- ✅ 在浏览器工具栏应该能看到插件图标
- ✅ 点击图标应该弹出完整的设置界面
- ✅ 在扩展程序页面应该显示"TaxSlayer 自动填写助手"

## 🧪 功能测试

### 1. 批量导入测试（推荐）
1. **准备测试数据**：
   ```
   <EMAIL>	testuser	password123	*********0	https://example.com/verify	John	<PERSON>	***********	01/01/1990	123 Main St	12345	12-3456789	Test Company	456 Business Ave	54321	50000	5000	50000	3100	50000	725	50000	2500	Test Bank	*********	*********
   ```

2. **导入测试**：
   - 点击插件图标打开弹窗
   - 在"批量导入数据"区域粘贴上述数据
   - 点击"导入并开始"按钮
   - 验证数据是否正确填充到表单中

### 2. 手动输入测试
1. **填写测试数据**：
   - 邮箱：<EMAIL>
   - 用户名：testuser
   - 密码：password123
   - 手机号：*********0
   - 手机验证链接：https://example.com/verify
   - 名字：John
   - 姓氏：Doe
   - SSN：***********
   - 出生日期：01/01/1990
   - 地址：123 Main St
   - 邮编：12345
   - 雇主EIN：12-3456789
   - 雇主公司：Test Company
   - （填写其他所有字段...）

2. **保存和加载测试**：
   - 点击"保存数据"
   - 点击"清空数据"
   - 点击"加载数据"验证数据恢复

### 3. 实际网站测试
1. **访问网站**：https://www.taxslayer.com/
2. **启动自动填写**：
   - 点击插件图标
   - 点击"开始自动填写"
   - 观察控制台输出（按F12打开开发者工具）

3. **验证自动化功能**：
   - ✅ 在注册页面应该出现"🚀 资料填放"按钮
   - ✅ 插件应该自动识别页面类型
   - ✅ 自动填写表单字段
   - ✅ 自动点击Continue按钮
   - ✅ 处理页面跳转

### 4. 状态控制测试
1. **启动/停止控制**：
   - 在自动填写过程中点击"停止自动填写"
   - 验证操作是否立即停止
   - 重新点击"开始自动填写"验证是否可以继续

2. **数据持久化测试**：
   - 关闭浏览器重新打开
   - 验证数据是否仍然保存
   - 验证执行状态是否正确恢复

## 调试和故障排除

### 查看日志
1. 按 F12 打开开发者工具
2. 切换到 Console 标签页
3. 查看插件输出的日志信息

### 常见问题

**问题1：插件图标不显示**
- 检查是否正确加载了扩展程序
- 确认 manifest.json 文件格式正确
- 重新加载扩展程序

**问题2：弹窗不显示**
- 检查 popup.html 和 popup.js 文件是否存在
- 查看扩展程序页面是否有错误信息
- 检查文件权限

**问题3：自动填写不工作**
- 确认在 TaxSlayer 网站上使用
- 检查控制台是否有错误信息
- 验证用户数据是否正确保存
- 检查网站页面结构是否发生变化

**问题4：页面识别失败**
- 查看控制台日志确认页面识别逻辑
- 检查页面元素选择器是否正确
- 可能需要更新页面识别逻辑

### 性能优化建议

1. **减少不必要的等待时间**：
   - 根据实际网络情况调整超时时间
   - 优化元素等待逻辑

2. **改进错误处理**：
   - 添加更多的错误恢复机制
   - 提供更详细的错误信息

3. **扩展功能**：
   - 添加更多页面类型支持
   - 实现更智能的数据验证
   - 添加进度显示功能

## 开发模式

### 修改代码后的测试流程
1. 修改代码文件
2. 在扩展程序页面点击刷新按钮
3. 重新测试功能
4. 查看控制台确认更改生效

### 添加新页面支持
1. 在 `content.js` 中添加新的页面检查函数
2. 实现对应的页面处理函数
3. 在 `identifyAndProcessStep` 函数中添加调用
4. 测试新页面的识别和处理

## 部署准备

如需打包为正式扩展程序：
1. 添加完整的图标文件
2. 完善错误处理和用户提示
3. 进行全面测试
4. 更新版本号
5. 使用 Chrome 扩展程序打包工具

## 安全注意事项

- 仅在可信任的网站使用
- 定期检查和更新代码
- 不要在生产环境中使用未经充分测试的版本
- 保护用户隐私数据
