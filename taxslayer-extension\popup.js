// popup.js - 处理插件弹窗界面逻辑

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const saveDataBtn = document.getElementById('saveData');
    const loadDataBtn = document.getElementById('loadData');
    const clearDataBtn = document.getElementById('clearData');
    const startAutoBtn = document.getElementById('startAuto');
    const stopAutoBtn = document.getElementById('stopAuto');
    const importBtn = document.getElementById('importBtn');
    const importDataTextarea = document.getElementById('importData');
    const statusDiv = document.getElementById('status');

    // 完整的表单字段列表（与原脚本一致）
    const fields = [
        'email', 'username', 'password', 'phone', 'phoneLink',
        'firstName', 'lastName', 'ssn', 'dob', 'address', 'zip',
        'employerEin', 'employerName', 'employerAddress', 'employerZip',
        'amount1', 'amount2', 'amount3', 'amount4', 'amount5', 'amount6',
        'amount16', 'amount17', 'bankName', 'routingNumber', 'accountNumber'
    ];

    // 显示状态消息
    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.classList.remove('hidden');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 3000);
    }

    // 保存数据到存储（与原脚本兼容的格式）
    async function saveData() {
        try {
            const data = {};
            let hasData = false;

            // 收集表单数据
            fields.forEach(field => {
                const element = document.getElementById(field);
                if (element && element.value.trim()) {
                    data[field] = element.value.trim();
                    hasData = true;
                }
            });

            if (!hasData) {
                showStatus('请至少填写一个字段', 'error');
                return;
            }

            // 获取现有数据
            const result = await chrome.storage.local.get(['taxslayerData']);
            let savedData = result.taxslayerData || [];

            // 添加新数据
            savedData.push(data);

            // 保存到 Chrome 存储（使用与原脚本相同的键名）
            await chrome.storage.local.set({
                taxslayerData: savedData,
                userData: data // 保持向后兼容
            });

            showStatus('数据保存成功！', 'success');

        } catch (error) {
            console.error('保存数据失败:', error);
            showStatus('保存数据失败', 'error');
        }
    }

    // 批量导入数据
    async function importData() {
        try {
            const text = importDataTextarea.value.trim();
            if (!text) {
                showStatus('请输入数据！', 'error');
                return;
            }

            const rows = text.split('\n');
            let importSuccess = false;
            let importCount = 0;

            for (const row of rows) {
                const [
                    email, username, password, phone, phoneLink,
                    firstName, lastName, ssn, dob, address, zip,
                    employerEin, employerName, employerAddress, employerZip,
                    amount1, amount2, amount3, amount4, amount5, amount6,
                    amount16, amount17, bankName, routingNumber, accountNumber
                ] = row.split('\t').map(item => item.trim());

                if (email && username && password && phone && phoneLink) {
                    const data = {
                        email, username, password, phone, phoneLink,
                        firstName, lastName, ssn, dob, address, zip,
                        employerEin, employerName, employerAddress, employerZip,
                        amount1, amount2, amount3, amount4, amount5, amount6,
                        amount16, amount17, bankName, routingNumber, accountNumber
                    };

                    // 获取现有数据
                    const result = await chrome.storage.local.get(['taxslayerData']);
                    let savedData = result.taxslayerData || [];

                    // 添加新数据
                    savedData.push(data);

                    // 保存数据
                    await chrome.storage.local.set({
                        taxslayerData: savedData,
                        userData: data // 最后一条数据作为当前数据
                    });

                    importSuccess = true;
                    importCount++;
                }
            }

            if (importSuccess) {
                importDataTextarea.value = '';
                showStatus(`成功导入 ${importCount} 条数据！`, 'success');

                // 加载最新数据到表单
                await loadData();

                // 自动开始填写
                setTimeout(() => {
                    startAutoFill();
                }, 1000);
            } else {
                showStatus('数据格式错误,请检查后重试！', 'error');
            }

        } catch (error) {
            console.error('导入数据失败:', error);
            showStatus('导入数据失败', 'error');
        }
    }

    // 从存储加载数据
    async function loadData() {
        try {
            const result = await chrome.storage.local.get(['taxslayerData', 'userData']);

            let dataToLoad = null;

            // 优先使用 taxslayerData 的最后一条数据
            if (result.taxslayerData && result.taxslayerData.length > 0) {
                dataToLoad = result.taxslayerData[result.taxslayerData.length - 1];
            } else if (result.userData) {
                dataToLoad = result.userData;
            }

            if (dataToLoad) {
                // 填充表单
                fields.forEach(field => {
                    const element = document.getElementById(field);
                    if (element && dataToLoad[field]) {
                        element.value = dataToLoad[field];
                    }
                });
                showStatus('数据加载成功！', 'success');
            } else {
                showStatus('未找到保存的数据', 'info');
            }

        } catch (error) {
            console.error('加载数据失败:', error);
            showStatus('加载数据失败', 'error');
        }
    }

    // 清空数据
    async function clearData() {
        try {
            // 清空表单
            fields.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    element.value = '';
                }
            });

            // 清空导入框
            importDataTextarea.value = '';

            // 清空存储
            await chrome.storage.local.remove(['taxslayerData', 'userData']);

            showStatus('数据已清空！', 'info');

        } catch (error) {
            console.error('清空数据失败:', error);
            showStatus('清空数据失败', 'error');
        }
    }

    // 开始自动填写
    async function startAutoFill() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('taxslayer.com')) {
                showStatus('请在 TaxSlayer 网站上使用此功能', 'error');
                return;
            }

            // 发送消息到 content script
            await chrome.tabs.sendMessage(tab.id, { 
                action: 'startAutoFill',
                userData: await getUserData()
            });
            
            showStatus('自动填写已启动', 'success');
            
        } catch (error) {
            console.error('启动自动填写失败:', error);
            showStatus('启动失败，请刷新页面后重试', 'error');
        }
    }

    // 停止自动填写
    async function stopAutoFill() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await chrome.tabs.sendMessage(tab.id, { 
                action: 'stopAutoFill'
            });
            
            showStatus('自动填写已停止', 'info');
            
        } catch (error) {
            console.error('停止自动填写失败:', error);
            showStatus('停止失败', 'error');
        }
    }

    // 获取用户数据
    async function getUserData() {
        const result = await chrome.storage.local.get(['taxslayerData', 'userData']);

        // 优先返回 taxslayerData 的最后一条数据
        if (result.taxslayerData && result.taxslayerData.length > 0) {
            return result.taxslayerData[result.taxslayerData.length - 1];
        }

        return result.userData || {};
    }

    // 绑定事件监听器
    saveDataBtn.addEventListener('click', saveData);
    loadDataBtn.addEventListener('click', loadData);
    clearDataBtn.addEventListener('click', clearData);
    startAutoBtn.addEventListener('click', startAutoFill);
    stopAutoBtn.addEventListener('click', stopAutoFill);
    importBtn.addEventListener('click', importData);

    // 页面加载时自动加载数据
    loadData();

    // 监听来自 content script 的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateStatus') {
            showStatus(message.message, message.type || 'info');
        }
    });
});
