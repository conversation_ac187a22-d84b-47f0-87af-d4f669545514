# TaxSlayer 自动填写助手

这是一个浏览器扩展程序，用于自动化 TaxSlayer 网站的报税流程，解决了原油猴脚本需要对焦的问题。

## 功能特点

- ✅ **无需对焦操作** - 插件在后台运行，不需要页面始终处于焦点状态
- ✅ **智能页面识别** - 自动识别当前页面类型并执行相应操作
- ✅ **数据持久化** - 用户数据和执行状态自动保存
- ✅ **错误恢复** - 支持中断后继续执行
- ✅ **用户友好界面** - 简洁的弹窗界面用于数据输入和控制

## 安装方法

1. 下载或克隆此项目到本地
2. 打开 Chrome 浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `taxslayer-extension`

## 使用方法

1. **数据输入**：
   - 点击浏览器工具栏中的插件图标
   - 在弹出的界面中填写个人信息
   - 点击"保存数据"按钮

2. **开始自动填写**：
   - 访问 TaxSlayer 网站
   - 点击插件图标，然后点击"开始自动填写"
   - 插件将自动识别页面并填写表单

3. **控制操作**：
   - 可以随时点击"停止自动填写"来停止操作
   - 数据会自动保存，下次可以继续使用

## 支持的页面

- 纳税人信息页面
- 地址信息页面
- 同意授权页面
- W-2 表单页面
- 报税状态页面
- 受抚养人页面
- 其他通用Continue页面

## 技术特点

### 相比油猴脚本的改进

1. **无对焦依赖**：
   - 移除了所有 `focus()` 和 `blur()` 调用
   - 使用事件模拟替代焦点操作
   - 后台运行，不受页面焦点影响

2. **更稳定的执行**：
   - 使用 MutationObserver 监听页面变化
   - 智能重试机制
   - 更好的错误处理

3. **状态管理**：
   - 跨页面状态保持
   - 执行步骤记录
   - 支持中断恢复

## 文件结构

```
taxslayer-extension/
├── manifest.json          # 扩展程序配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 主要自动化逻辑
├── background.js          # 后台服务工作者
├── icons/                 # 图标文件夹
└── README.md             # 说明文档
```

## 注意事项

- 请确保在 TaxSlayer 官方网站使用
- 建议在使用前备份重要数据
- 如遇到问题，可以停止自动填写并手动操作
- 插件仅用于学习和个人使用

## 开发说明

如需修改或扩展功能：

1. 修改 `content.js` 添加新的页面处理逻辑
2. 更新 `popup.html` 和 `popup.js` 修改用户界面
3. 在 `background.js` 中添加后台处理逻辑
4. 重新加载扩展程序测试

## 许可证

本项目仅供学习和个人使用。请遵守相关法律法规和网站使用条款。
