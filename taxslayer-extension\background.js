// background.js - 后台服务工作者

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
    console.log('TaxSlayer 自动填写助手已安装');
    
    // 初始化存储
    chrome.storage.local.set({
        isAutoFillActive: false,
        executedSteps: {},
        currentStep: null
    });
});

// 监听来自 content script 和 popup 的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background 收到消息:', message);
    
    switch (message.action) {
        case 'saveExecutedSteps':
            handleSaveExecutedSteps(message.steps);
            break;
            
        case 'getExecutedSteps':
            handleGetExecutedSteps(sendResponse);
            return true; // 保持消息通道开放
            
        case 'updateCurrentStep':
            handleUpdateCurrentStep(message.step);
            break;
            
        case 'setAutoFillStatus':
            handleSetAutoFillStatus(message.isActive);
            break;
            
        case 'getAutoFillStatus':
            handleGetAutoFillStatus(sendResponse);
            return true;
            
        case 'logMessage':
            console.log(`[${message.level}] ${message.message}`);
            break;
            
        case 'makeRequest':
            handleMakeRequest(message, sendResponse);
            return true;
            
        default:
            console.log('未知消息类型:', message.action);
    }
});

// 保存执行步骤状态
async function handleSaveExecutedSteps(steps) {
    try {
        await chrome.storage.local.set({ executedSteps: steps });
        console.log('执行步骤状态已保存');
    } catch (error) {
        console.error('保存执行步骤失败:', error);
    }
}

// 获取执行步骤状态
async function handleGetExecutedSteps(sendResponse) {
    try {
        const result = await chrome.storage.local.get(['executedSteps']);
        sendResponse({ success: true, steps: result.executedSteps || {} });
    } catch (error) {
        console.error('获取执行步骤失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 更新当前步骤
async function handleUpdateCurrentStep(step) {
    try {
        await chrome.storage.local.set({ currentStep: step });
        console.log('当前步骤已更新:', step);
    } catch (error) {
        console.error('更新当前步骤失败:', error);
    }
}

// 设置自动填写状态
async function handleSetAutoFillStatus(isActive) {
    try {
        await chrome.storage.local.set({ isAutoFillActive: isActive });
        console.log('自动填写状态已更新:', isActive);
        
        // 通知所有 TaxSlayer 标签页状态变化
        const tabs = await chrome.tabs.query({ url: 'https://www.taxslayer.com/*' });
        tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'autoFillStatusChanged',
                isActive: isActive
            }).catch(() => {
                // 忽略无法发送消息的标签页
            });
        });
    } catch (error) {
        console.error('设置自动填写状态失败:', error);
    }
}

// 获取自动填写状态
async function handleGetAutoFillStatus(sendResponse) {
    try {
        const result = await chrome.storage.local.get(['isAutoFillActive']);
        sendResponse({ success: true, isActive: result.isAutoFillActive || false });
    } catch (error) {
        console.error('获取自动填写状态失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 处理网络请求（替代 GM_xmlhttpRequest）
async function handleMakeRequest(message, sendResponse) {
    try {
        const { url, method = 'GET', headers = {} } = message;
        
        const response = await fetch(url, {
            method: method,
            headers: headers
        });
        
        const responseText = await response.text();
        
        sendResponse({
            success: true,
            responseText: responseText,
            status: response.status,
            statusText: response.statusText
        });
        
    } catch (error) {
        console.error('网络请求失败:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

// 监听标签页更新，重置相关状态
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('taxslayer.com')) {
        console.log('TaxSlayer 页面加载完成:', tab.url);
        
        // 可以在这里发送页面加载完成的消息给 content script
        chrome.tabs.sendMessage(tabId, {
            action: 'pageLoaded',
            url: tab.url
        }).catch(() => {
            // 忽略无法发送消息的情况
        });
    }
});

// 监听标签页关闭，清理相关状态
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    console.log('标签页已关闭:', tabId);
    // 可以在这里清理与该标签页相关的状态
});

// 处理扩展程序图标点击
chrome.action.onClicked.addListener((tab) => {
    // 这个事件在有 popup 时不会触发，但保留以备将来使用
    console.log('扩展程序图标被点击');
});

// 定期清理过期数据（可选）
setInterval(async () => {
    try {
        // 清理超过24小时的临时数据
        const result = await chrome.storage.local.get(['lastCleanup']);
        const now = Date.now();
        const lastCleanup = result.lastCleanup || 0;
        
        if (now - lastCleanup > 24 * 60 * 60 * 1000) { // 24小时
            // 执行清理操作
            await chrome.storage.local.set({ lastCleanup: now });
            console.log('定期清理完成');
        }
    } catch (error) {
        console.error('定期清理失败:', error);
    }
}, 60 * 60 * 1000); // 每小时检查一次
