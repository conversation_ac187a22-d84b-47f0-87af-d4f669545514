// popup.js - 处理插件弹窗界面逻辑

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const saveDataBtn = document.getElementById('saveData');
    const loadDataBtn = document.getElementById('loadData');
    const startAutoBtn = document.getElementById('startAuto');
    const stopAutoBtn = document.getElementById('stopAuto');
    const statusDiv = document.getElementById('status');

    // 表单字段
    const fields = [
        'firstName', 'lastName', 'ssn', 'dob', 
        'address', 'zip', 'employerName', 'employerEin'
    ];

    // 显示状态消息
    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.classList.remove('hidden');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 3000);
    }

    // 保存数据到存储
    async function saveData() {
        try {
            const data = {};
            let hasData = false;

            // 收集表单数据
            fields.forEach(field => {
                const element = document.getElementById(field);
                if (element && element.value.trim()) {
                    data[field] = element.value.trim();
                    hasData = true;
                }
            });

            if (!hasData) {
                showStatus('请至少填写一个字段', 'error');
                return;
            }

            // 保存到 Chrome 存储
            await chrome.storage.local.set({ userData: data });
            showStatus('数据保存成功！', 'success');
            
        } catch (error) {
            console.error('保存数据失败:', error);
            showStatus('保存数据失败', 'error');
        }
    }

    // 从存储加载数据
    async function loadData() {
        try {
            const result = await chrome.storage.local.get(['userData']);
            
            if (result.userData) {
                // 填充表单
                fields.forEach(field => {
                    const element = document.getElementById(field);
                    if (element && result.userData[field]) {
                        element.value = result.userData[field];
                    }
                });
                showStatus('数据加载成功！', 'success');
            } else {
                showStatus('未找到保存的数据', 'info');
            }
            
        } catch (error) {
            console.error('加载数据失败:', error);
            showStatus('加载数据失败', 'error');
        }
    }

    // 开始自动填写
    async function startAutoFill() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('taxslayer.com')) {
                showStatus('请在 TaxSlayer 网站上使用此功能', 'error');
                return;
            }

            // 发送消息到 content script
            await chrome.tabs.sendMessage(tab.id, { 
                action: 'startAutoFill',
                userData: await getUserData()
            });
            
            showStatus('自动填写已启动', 'success');
            
        } catch (error) {
            console.error('启动自动填写失败:', error);
            showStatus('启动失败，请刷新页面后重试', 'error');
        }
    }

    // 停止自动填写
    async function stopAutoFill() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await chrome.tabs.sendMessage(tab.id, { 
                action: 'stopAutoFill'
            });
            
            showStatus('自动填写已停止', 'info');
            
        } catch (error) {
            console.error('停止自动填写失败:', error);
            showStatus('停止失败', 'error');
        }
    }

    // 获取用户数据
    async function getUserData() {
        const result = await chrome.storage.local.get(['userData']);
        return result.userData || {};
    }

    // 绑定事件监听器
    saveDataBtn.addEventListener('click', saveData);
    loadDataBtn.addEventListener('click', loadData);
    startAutoBtn.addEventListener('click', startAutoFill);
    stopAutoBtn.addEventListener('click', stopAutoFill);

    // 页面加载时自动加载数据
    loadData();

    // 监听来自 content script 的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateStatus') {
            showStatus(message.message, message.type || 'info');
        }
    });
});
