<!DOCTYPE html>
<html>
<head>
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        .icon-size {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TaxSlayer 插件图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下面的"生成图标"按钮</li>
                <li>右键点击每个图标选择"另存为"</li>
                <li>保存为对应的文件名：icon16.png, icon48.png, icon128.png</li>
                <li>将文件放在 icons/ 文件夹中</li>
            </ol>
        </div>

        <button onclick="generateIcons()">生成图标</button>
        <button onclick="downloadAll()">下载所有图标</button>

        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button onclick="downloadIcon(16)">下载 icon16.png</button>
            </div>
            
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button onclick="downloadIcon(48)">下载 icon48.png</button>
            </div>
            
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button onclick="downloadIcon(128)">下载 icon128.png</button>
            </div>
        </div>
    </div>

    <script>
        // SVG 图标数据
        const svgData = `<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
            <circle cx="64" cy="64" r="60" fill="#4CAF50" stroke="#2E7D32" stroke-width="4"/>
            <rect x="32" y="28" width="64" height="72" rx="8" fill="#FFFFFF" stroke="#333" stroke-width="2"/>
            <rect x="38" y="34" width="52" height="16" rx="2" fill="#1976D2" stroke="#0D47A1" stroke-width="1"/>
            <text x="64" y="46" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TAX</text>
            <rect x="40" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="54" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="68" y="56" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="82" y="56" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
            <rect x="40" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="54" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="68" y="68" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="82" y="68" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
            <rect x="40" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="54" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="68" y="80" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="82" y="80" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
            <rect x="40" y="92" width="24" height="8" rx="1" fill="#4CAF50" stroke="#2E7D32" stroke-width="0.5"/>
            <rect x="68" y="92" width="10" height="8" rx="1" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="0.5"/>
            <rect x="82" y="92" width="10" height="8" rx="1" fill="#FF9800" stroke="#F57C00" stroke-width="0.5"/>
            <text x="45" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">7</text>
            <text x="59" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">8</text>
            <text x="73" y="62" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">9</text>
            <text x="87" y="62" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">÷</text>
            <text x="45" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">4</text>
            <text x="59" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">5</text>
            <text x="73" y="74" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">6</text>
            <text x="87" y="74" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">×</text>
            <text x="45" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">1</text>
            <text x="59" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">2</text>
            <text x="73" y="86" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">3</text>
            <text x="87" y="86" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">-</text>
            <text x="52" y="98" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">0</text>
            <text x="73" y="98" text-anchor="middle" fill="#333" font-family="Arial, sans-serif" font-size="6">.</text>
            <text x="87" y="98" text-anchor="middle" fill="#FFF" font-family="Arial, sans-serif" font-size="6">+</text>
            <circle cx="100" cy="28" r="12" fill="#FF5722" stroke="#D84315" stroke-width="2"/>
            <text x="100" y="32" text-anchor="middle" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="8" font-weight="bold">A</text>
            <path d="M 20 110 Q 64 105 108 110" stroke="#4CAF50" stroke-width="2" fill="none"/>
            <circle cx="25" cy="108" r="2" fill="#4CAF50"/>
            <circle cx="103" cy="108" r="2" fill="#4CAF50"/>
        </svg>`;

        function generateIcons() {
            const sizes = [16, 48, 128];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // 创建图像
                const img = new Image();
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = function() {
                    ctx.clearRect(0, 0, size, size);
                    ctx.drawImage(img, 0, 0, size, size);
                    URL.revokeObjectURL(url);
                };
                
                img.src = url;
            });
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadAll() {
            setTimeout(() => downloadIcon(16), 100);
            setTimeout(() => downloadIcon(48), 200);
            setTimeout(() => downloadIcon(128), 300);
        }

        // 页面加载时自动生成图标
        window.onload = function() {
            setTimeout(generateIcons, 500);
        };
    </script>
</body>
</html>
