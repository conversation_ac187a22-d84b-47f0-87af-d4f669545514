<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button-group {
            margin-top: 20px;
            text-align: center;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>TaxSlayer 自动填写助手</h3>
    </div>

    <div id="dataForm">
        <div class="form-group">
            <label for="firstName">名字:</label>
            <input type="text" id="firstName" placeholder="请输入名字">
        </div>
        
        <div class="form-group">
            <label for="lastName">姓氏:</label>
            <input type="text" id="lastName" placeholder="请输入姓氏">
        </div>
        
        <div class="form-group">
            <label for="ssn">SSN:</label>
            <input type="text" id="ssn" placeholder="***********">
        </div>
        
        <div class="form-group">
            <label for="dob">出生日期:</label>
            <input type="text" id="dob" placeholder="MM/DD/YYYY">
        </div>
        
        <div class="form-group">
            <label for="address">地址:</label>
            <input type="text" id="address" placeholder="请输入地址">
        </div>
        
        <div class="form-group">
            <label for="zip">邮编:</label>
            <input type="text" id="zip" placeholder="12345">
        </div>
        
        <div class="form-group">
            <label for="employerName">雇主名称:</label>
            <input type="text" id="employerName" placeholder="公司名称">
        </div>
        
        <div class="form-group">
            <label for="employerEin">雇主EIN:</label>
            <input type="text" id="employerEin" placeholder="12-3456789">
        </div>
    </div>

    <div class="button-group">
        <button id="saveData" class="btn-primary">保存数据</button>
        <button id="loadData" class="btn-success">加载数据</button>
        <button id="startAuto" class="btn-warning">开始自动填写</button>
        <button id="stopAuto" class="btn-danger">停止自动填写</button>
    </div>

    <div id="status" class="status hidden"></div>

    <script src="popup.js"></script>
</body>
</html>
