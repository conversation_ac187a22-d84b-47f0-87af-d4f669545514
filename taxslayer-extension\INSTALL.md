# 安装和测试指南

## 快速安装步骤

### 1. 准备图标文件（可选）
在 `icons/` 文件夹中添加以下图标文件：
- `icon16.png` (16x16 像素)
- `icon48.png` (48x48 像素)  
- `icon128.png` (128x128 像素)

如果没有图标文件，可以暂时跳过此步骤，插件仍然可以正常工作。

### 2. 加载扩展程序
1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `taxslayer-extension` 文件夹
6. 确认加载成功

### 3. 验证安装
- 在浏览器工具栏应该能看到插件图标
- 点击图标应该弹出设置界面
- 在扩展程序页面应该显示"TaxSlayer 自动填写助手"

## 使用测试

### 1. 数据输入测试
1. 点击插件图标打开弹窗
2. 填写测试数据：
   - 名字：John
   - 姓氏：Doe  
   - SSN：***********
   - 出生日期：01/01/1990
   - 地址：123 Main St
   - 邮编：12345
   - 雇主名称：Test Company
   - 雇主EIN：12-3456789
3. 点击"保存数据"
4. 点击"加载数据"验证数据是否正确保存

### 2. 功能测试
1. 访问 TaxSlayer 网站：https://www.taxslayer.com/
2. 点击插件图标
3. 点击"开始自动填写"
4. 观察控制台输出（按F12打开开发者工具）
5. 验证插件是否正确识别页面并执行操作

### 3. 状态控制测试
1. 在自动填写过程中点击"停止自动填写"
2. 验证操作是否停止
3. 重新点击"开始自动填写"验证是否可以继续

## 调试和故障排除

### 查看日志
1. 按 F12 打开开发者工具
2. 切换到 Console 标签页
3. 查看插件输出的日志信息

### 常见问题

**问题1：插件图标不显示**
- 检查是否正确加载了扩展程序
- 确认 manifest.json 文件格式正确
- 重新加载扩展程序

**问题2：弹窗不显示**
- 检查 popup.html 和 popup.js 文件是否存在
- 查看扩展程序页面是否有错误信息
- 检查文件权限

**问题3：自动填写不工作**
- 确认在 TaxSlayer 网站上使用
- 检查控制台是否有错误信息
- 验证用户数据是否正确保存
- 检查网站页面结构是否发生变化

**问题4：页面识别失败**
- 查看控制台日志确认页面识别逻辑
- 检查页面元素选择器是否正确
- 可能需要更新页面识别逻辑

### 性能优化建议

1. **减少不必要的等待时间**：
   - 根据实际网络情况调整超时时间
   - 优化元素等待逻辑

2. **改进错误处理**：
   - 添加更多的错误恢复机制
   - 提供更详细的错误信息

3. **扩展功能**：
   - 添加更多页面类型支持
   - 实现更智能的数据验证
   - 添加进度显示功能

## 开发模式

### 修改代码后的测试流程
1. 修改代码文件
2. 在扩展程序页面点击刷新按钮
3. 重新测试功能
4. 查看控制台确认更改生效

### 添加新页面支持
1. 在 `content.js` 中添加新的页面检查函数
2. 实现对应的页面处理函数
3. 在 `identifyAndProcessStep` 函数中添加调用
4. 测试新页面的识别和处理

## 部署准备

如需打包为正式扩展程序：
1. 添加完整的图标文件
2. 完善错误处理和用户提示
3. 进行全面测试
4. 更新版本号
5. 使用 Chrome 扩展程序打包工具

## 安全注意事项

- 仅在可信任的网站使用
- 定期检查和更新代码
- 不要在生产环境中使用未经充分测试的版本
- 保护用户隐私数据
