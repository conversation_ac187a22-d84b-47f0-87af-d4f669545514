// 简单的图标创建脚本
// 在浏览器控制台中运行此代码来生成图标

function createSimpleIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // 背景圆形
    ctx.fillStyle = '#4CAF50';
    ctx.beginPath();
    ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
    ctx.fill();
    
    // 边框
    ctx.strokeStyle = '#2E7D32';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 计算器主体
    const rectWidth = size * 0.5;
    const rectHeight = size * 0.56;
    const rectX = (size - rectWidth) / 2;
    const rectY = size * 0.22;
    
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(rectX, rectY, rectWidth, rectHeight);
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);
    
    // 显示屏
    const screenWidth = rectWidth * 0.8;
    const screenHeight = size * 0.125;
    const screenX = rectX + (rectWidth - screenWidth) / 2;
    const screenY = rectY + size * 0.047;
    
    ctx.fillStyle = '#1976D2';
    ctx.fillRect(screenX, screenY, screenWidth, screenHeight);
    
    // TAX 文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `bold ${size * 0.078}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText('TAX', size/2, screenY + screenHeight * 0.7);
    
    // 按钮网格
    const buttonSize = size * 0.078;
    const buttonSpacing = size * 0.109;
    const startX = rectX + size * 0.063;
    const startY = rectY + size * 0.22;
    
    for (let row = 0; row < 4; row++) {
        for (let col = 0; col < 4; col++) {
            const x = startX + col * buttonSpacing;
            const y = startY + row * buttonSpacing;
            
            // 最后一行的第一个按钮（0）要宽一些
            if (row === 3 && col === 0) {
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(x, y, buttonSize * 1.8, buttonSize);
                col++; // 跳过下一个位置
            } else if (col === 3) {
                // 右侧操作按钮
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(x, y, buttonSize, buttonSize);
            } else {
                // 普通数字按钮
                ctx.fillStyle = '#E0E0E0';
                ctx.fillRect(x, y, buttonSize, buttonSize);
            }
        }
    }
    
    // 自动化标识
    const autoX = size * 0.78;
    const autoY = size * 0.22;
    const autoRadius = size * 0.094;
    
    ctx.fillStyle = '#FF5722';
    ctx.beginPath();
    ctx.arc(autoX, autoY, autoRadius, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `bold ${size * 0.063}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText('A', autoX, autoY + size * 0.016);
    
    return canvas;
}

// 生成并下载图标
function generateAndDownloadIcons() {
    const sizes = [16, 48, 128];
    
    sizes.forEach((size, index) => {
        setTimeout(() => {
            const canvas = createSimpleIcon(size);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log(`Generated icon${size}.png`);
        }, index * 500);
    });
}

// 使用说明
console.log('图标生成脚本已加载！');
console.log('运行 generateAndDownloadIcons() 来生成并下载所有图标');
console.log('或者运行 createSimpleIcon(size) 来生成特定尺寸的图标');

// 如果在浏览器环境中，自动生成图标
if (typeof window !== 'undefined') {
    console.log('检测到浏览器环境，3秒后自动生成图标...');
    setTimeout(() => {
        generateAndDownloadIcons();
    }, 3000);
}
