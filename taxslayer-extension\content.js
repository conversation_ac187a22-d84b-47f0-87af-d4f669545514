// content.js - 主要的自动化逻辑脚本

(function() {
    'use strict';

    // 全局变量
    let isAutoFillActive = false;
    let userData = {};
    let executedSteps = {};
    let currentObserver = null;

    // 初始化
    console.log('TaxSlayer 自动填写助手已加载');

    // 监听来自 popup 和 background 的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Content script 收到消息:', message);
        
        switch (message.action) {
            case 'startAutoFill':
                startAutoFill(message.userData);
                sendResponse({ success: true });
                break;
                
            case 'stopAutoFill':
                stopAutoFill();
                sendResponse({ success: true });
                break;
                
            case 'autoFillStatusChanged':
                isAutoFillActive = message.isActive;
                break;
                
            case 'pageLoaded':
                handlePageLoaded();
                break;
                
            default:
                console.log('未知消息类型:', message.action);
        }
    });

    // 启动自动填写
    async function startAutoFill(userDataParam) {
        try {
            userData = userDataParam || {};
            isAutoFillActive = true;
            
            // 通知 background script 状态变化
            chrome.runtime.sendMessage({
                action: 'setAutoFillStatus',
                isActive: true
            });
            
            // 获取已执行的步骤
            const response = await chrome.runtime.sendMessage({
                action: 'getExecutedSteps'
            });
            
            if (response.success) {
                executedSteps = response.steps;
            }
            
            console.log('自动填写已启动');
            
            // 等待页面完全加载
            await waitForPageReady();
            
            // 开始处理当前页面
            processCurrentPage();
            
        } catch (error) {
            console.error('启动自动填写失败:', error);
        }
    }

    // 停止自动填写
    function stopAutoFill() {
        isAutoFillActive = false;
        
        // 停止观察器
        if (currentObserver) {
            currentObserver.disconnect();
            currentObserver = null;
        }
        
        // 通知 background script 状态变化
        chrome.runtime.sendMessage({
            action: 'setAutoFillStatus',
            isActive: false
        });
        
        console.log('自动填写已停止');
    }

    // 等待页面准备就绪
    function waitForPageReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    // 处理页面加载完成
    function handlePageLoaded() {
        if (isAutoFillActive) {
            setTimeout(() => {
                processCurrentPage();
            }, 1000);
        }
    }

    // 等待元素出现（优化版，无需对焦）
    function waitForElement(selector, timeout = 15000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function checkElement() {
                const element = document.querySelector(selector);
                const elapsedTime = Date.now() - startTime;
                
                if (element) {
                    console.log(`✅ 找到元素: ${selector}, 耗时: ${elapsedTime}ms`);
                    resolve(element);
                    return;
                }
                
                if (elapsedTime > timeout) {
                    console.log(`❌ 等待元素超时: ${selector}, 已等待: ${timeout}ms`);
                    reject(new Error(`元素等待超时: ${selector}`));
                    return;
                }
                
                requestAnimationFrame(checkElement);
            }
            
            checkElement();
        });
    }

    // 优化的输入模拟（无需对焦）
    async function simulateTyping(element, text) {
        if (!element) {
            console.error('❌ 输入元素不存在');
            return false;
        }

        try {
            // 清空输入框
            element.value = '';
            
            // 触发 focus 事件（但不实际对焦）
            element.dispatchEvent(new Event('focus', { bubbles: true }));
            
            // 设置新值
            element.value = text;
            
            // 触发必要的事件
            const events = ['input', 'change', 'keyup', 'blur'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                element.dispatchEvent(event);
            });
            
            // 移除invalid类
            element.classList.remove('text-input-box__invalid');
            
            // 触发React事件（如果需要）
            if (element._valueTracker) {
                element._valueTracker.setValue('');
            }
            
            console.log(`✅ 成功输入文本: ${text.substring(0, 10)}...`);
            return true;
            
        } catch (error) {
            console.error('❌ 输入模拟失败:', error);
            return false;
        }
    }

    // 优化的点击操作（无需对焦）
    function simulateClick(element) {
        if (!element) {
            console.error('元素不存在，无法点击');
            return false;
        }

        try {
            // 多种点击方法
            element.click();
            
            // 触发鼠标事件
            const mouseEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                buttons: 1
            });
            element.dispatchEvent(mouseEvent);
            
            // 如果是表单元素，触发相关事件
            if (element.tagName === 'INPUT' || element.tagName === 'BUTTON') {
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            console.log('✅ 元素点击完成:', element.tagName, element.id || element.className);
            return true;
            
        } catch (error) {
            console.error('❌ 点击操作失败:', error);
            return false;
        }
    }

    // 等待页面跳转完成
    function waitForPageTransition(timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            let checkCount = 0;
            const maxChecks = timeout / 500;
            
            const checkInterval = setInterval(() => {
                checkCount++;
                
                if (document.readyState === 'complete' &&
                    !document.querySelector('.loading-spinner') &&
                    !document.querySelector('.loading-overlay')) {
                    clearInterval(checkInterval);
                    console.log('页面跳转完成');
                    resolve();
                    return;
                }
                
                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    console.log('等待页面跳转超时');
                    resolve();
                }
            }, 500);
        });
    }

    // 处理当前页面
    async function processCurrentPage() {
        if (!isAutoFillActive) {
            return;
        }
        
        try {
            const url = window.location.href;
            console.log('处理页面:', url);
            
            // 根据页面内容判断当前步骤
            await identifyAndProcessStep();
            
        } catch (error) {
            console.error('处理页面失败:', error);
        }
    }



    // 设置页面变化观察器
    function setupPageChangeObserver() {
        if (currentObserver) {
            currentObserver.disconnect();
        }
        
        currentObserver = new MutationObserver((mutations) => {
            let shouldReprocess = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldReprocess = true;
                }
            });
            
            if (shouldReprocess && isAutoFillActive) {
                setTimeout(() => {
                    processCurrentPage();
                }, 1000);
            }
        });
        
        currentObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 检查纳税人信息页面
    async function checkTaxpayerInfoPage() {
        try {
            const titleElement = document.querySelector('div.col-md-12 h3');
            return titleElement && titleElement.textContent === 'Taxpayer\'s Information';
        } catch (error) {
            return false;
        }
    }

    // 处理纳税人信息页面
    async function handleTaxpayerInfo() {
        if (executedSteps.taxpayerInfo) {
            console.log('纳税人信息页面已处理过，跳过...');
            return;
        }

        console.log('开始处理纳税人信息页面...');

        try {
            // 等待输入框加载
            const firstNameInput = await waitForElement('#info\\.firstName');
            const lastNameInput = await waitForElement('#info\\.lastName');
            const ssnInput = await waitForElement('#info\\.socialSecurityNumber');
            const dobInput = await waitForElement('#info\\.dateOfBirth');

            // 按顺序填写
            await simulateTyping(firstNameInput, userData.firstName || '');
            await new Promise(resolve => setTimeout(resolve, 500));

            await simulateTyping(lastNameInput, userData.lastName || '');
            await new Promise(resolve => setTimeout(resolve, 500));

            await simulateTyping(ssnInput, userData.ssn || '');
            await new Promise(resolve => setTimeout(resolve, 500));

            await simulateTyping(dobInput, userData.dob || '');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 点击提交按钮
            const submitButton = await waitForElement('#btnSubmit');
            simulateClick(submitButton);

            // 标记为已完成
            executedSteps.taxpayerInfo = true;
            await saveExecutedSteps();

            // 等待页面跳转
            await waitForPageTransition();

            console.log('纳税人信息页面处理完成');

        } catch (error) {
            console.error('处理纳税人信息页面失败:', error);
        }
    }

    // 检查地址信息页面
    async function checkAddressInfoPage() {
        try {
            const addressLabel = document.querySelector('label[for="address.line1"]');
            return addressLabel && addressLabel.textContent.includes('Address (street number & name)');
        } catch (error) {
            return false;
        }
    }

    // 处理地址信息页面
    async function handleAddressInfo() {
        if (executedSteps.addressInfo) {
            console.log('地址信息页面已处理过，跳过...');
            return;
        }

        console.log('开始处理地址信息页面...');

        try {
            // 填写地址
            const addressInput = await waitForElement('#address\\.line1');
            await simulateTyping(addressInput, userData.address || '');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 填写邮编
            const zipInput = await waitForElement('#address\\.zip');
            await simulateTyping(zipInput, userData.zip || '');

            // 等待城市和州自动填充
            await waitForCityAndStateLoad();

            // 选择居民状态
            const residentRadio = await waitForElement('#residentStatus_2Radio');
            simulateClick(residentRadio);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 提交
            const submitButton = await waitForElement('#btnSubmit');
            simulateClick(submitButton);

            // 标记为已完成
            executedSteps.addressInfo = true;
            await saveExecutedSteps();

            await waitForPageTransition();
            console.log('地址信息页面处理完成');

        } catch (error) {
            console.error('处理地址信息页面失败:', error);
        }
    }

    // 等待城市和州加载
    async function waitForCityAndStateLoad() {
        let attempts = 0;
        const maxAttempts = 20;

        while (attempts < maxAttempts) {
            const cityInput = document.querySelector('#address\\.city');
            const stateSelect = document.querySelector('#address\\.state');

            if (cityInput && stateSelect &&
                cityInput.value && cityInput.value.trim() !== '' &&
                stateSelect.value && stateSelect.value !== 'Select') {
                console.log('城市和州信息已加载完成');
                return;
            }

            attempts++;
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log('城市和州信息加载超时，继续下一步');
    }

    // 检查同意页面
    async function checkConsentPage() {
        try {
            const titleElement = document.querySelector('h1#page-title');
            return titleElement && titleElement.textContent === 'We need your approval on a couple things';
        } catch (error) {
            return false;
        }
    }

    // 处理同意页面
    async function handleConsent() {
        if (executedSteps.consent) {
            console.log('同意页面已处理过，跳过...');
            return;
        }

        console.log('开始处理同意页面...');

        try {
            const fullName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();

            // 第一个同意选项
            const useConsentCheckbox = await waitForElement('#consentToUse_UserConsent');
            simulateClick(useConsentCheckbox);
            await new Promise(resolve => setTimeout(resolve, 500));

            const useSignatureInput = await waitForElement('input[name="consentToUse_TpSignature"]');
            await simulateTyping(useSignatureInput, fullName);
            await new Promise(resolve => setTimeout(resolve, 500));

            // 第二个同意选项
            const disclosureConsentCheckbox = await waitForElement('#consentToDisclosure_UserConsent');
            simulateClick(disclosureConsentCheckbox);
            await new Promise(resolve => setTimeout(resolve, 500));

            const disclosureSignatureInput = await waitForElement('input[name="consentToDisclosure_TpSignature"]');
            await simulateTyping(disclosureSignatureInput, fullName);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 提交
            const submitButton = await waitForElement('#btnSubmit');
            simulateClick(submitButton);

            // 标记为已完成
            executedSteps.consent = true;
            await saveExecutedSteps();

            await waitForPageTransition();
            console.log('同意页面处理完成');

        } catch (error) {
            console.error('处理同意页面失败:', error);
        }
    }

    // 保存执行步骤状态
    async function saveExecutedSteps() {
        try {
            await chrome.runtime.sendMessage({
                action: 'saveExecutedSteps',
                steps: executedSteps
            });
        } catch (error) {
            console.error('保存执行步骤失败:', error);
        }
    }

    // 检查W2表单页面
    async function checkW2FormPage() {
        try {
            const titleElement = document.querySelector('h1[data-testid="page-title-h1"]');
            return titleElement && titleElement.textContent === 'Wage and tax statement';
        } catch (error) {
            return false;
        }
    }

    // 处理W2表单页面
    async function handleW2Form() {
        if (executedSteps.w2Form) {
            console.log('W2表单页面已处理过，跳过...');
            return;
        }

        console.log('开始处理W2表单页面...');

        try {
            // 填写雇主EIN
            const einInput = await waitForElement('#employer\\.ein');
            await simulateTyping(einInput, userData.employerEin || '');

            // 填写雇主名称
            const employerNameInput = await waitForElement('#employer\\.name');
            await simulateTyping(employerNameInput, userData.employerName || '');

            // 填写雇主地址
            const employerAddressInput = await waitForElement('#employer\\.address\\.street');
            await simulateTyping(employerAddressInput, userData.employerAddress || '');

            // 填写雇主邮编
            const employerZipInput = await waitForElement('#employer\\.address\\.zip');
            await simulateTyping(employerZipInput, userData.employerZip || '');

            // 等待雇主城市和州加载
            await waitForEmployerCityAndStateLoad();

            // 填写州EIN
            const stateEinInput = await waitForElement('#states\\[0\\]\\.ein');
            await simulateTyping(stateEinInput, userData.employerEin || '');

            // 填写工资信息
            await fillWageInformation();

            // 选择州
            await selectStateByZip(userData.zip || '');

            // 提交
            const continueButton = await waitForElement('#btnSubmit');
            simulateClick(continueButton);

            // 标记为已完成
            executedSteps.w2Form = true;
            await saveExecutedSteps();

            await waitForPageTransition();
            console.log('W2表单页面处理完成');

        } catch (error) {
            console.error('处理W2表单页面失败:', error);
        }
    }

    // 等待雇主城市和州加载
    async function waitForEmployerCityAndStateLoad() {
        let attempts = 0;
        const maxAttempts = 6;

        while (attempts < maxAttempts) {
            const cityInput = document.querySelector('#employer\\.address\\.city');
            const stateSelect = document.querySelector('#employer\\.address\\.state');

            if (cityInput && stateSelect &&
                cityInput.value && cityInput.value.trim() !== '' &&
                stateSelect.value && stateSelect.value !== 'Select') {
                console.log('雇主城市和州信息已加载完成');
                return;
            }

            attempts++;
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log('雇主城市和州信息加载超时，继续下一步');
    }

    // 填写工资信息
    async function fillWageInformation() {
        const wageFields = [
            { selector: '#information\\.federalWages', value: userData.amount1 },
            { selector: '#information\\.federalWithheld', value: userData.amount2 },
            { selector: '#information\\.socialSecurityWages', value: userData.amount3 },
            { selector: '#information\\.socialSecurityWithheld', value: userData.amount4 },
            { selector: '#information\\.medicareWages', value: userData.amount5 },
            { selector: '#information\\.medicareWithheld', value: userData.amount6 },
            { selector: '#states\\[0\\]\\.wage', value: userData.amount16 },
            { selector: '#states\\[0\\]\\.tax', value: userData.amount17 }
        ];

        for (const field of wageFields) {
            try {
                const input = document.querySelector(field.selector);
                if (input && field.value) {
                    await simulateTyping(input, field.value);
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            } catch (error) {
                console.error(`填写字段失败 ${field.selector}:`, error);
            }
        }
    }

    // 根据邮编选择州
    async function selectStateByZip(zipCode) {
        if (!zipCode) return;

        try {
            // 尝试通过API获取州信息
            const state = await getStateByZip(zipCode);
            await selectState(state);
        } catch (error) {
            console.error('通过API获取州信息失败，使用回退方法:', error);
            const fallbackState = getFallbackStateByZip(zipCode);
            await selectState(fallbackState);
        }
    }

    // 通过API获取州信息
    async function getStateByZip(zipCode) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'makeRequest',
                url: `https://api.zippopotam.us/us/${zipCode}`,
                method: 'GET'
            }, (response) => {
                if (response.success) {
                    try {
                        const data = JSON.parse(response.responseText);
                        const state = data.places[0].state;
                        resolve(state);
                    } catch (error) {
                        reject(error);
                    }
                } else {
                    reject(new Error(response.error));
                }
            });
        });
    }

    // 回退方法：根据邮编第一位数字选择州
    function getFallbackStateByZip(zipCode) {
        const zipFirstDigit = zipCode.charAt(0);
        const stateMap = {
            '0': 'Massachusetts', '1': 'Massachusetts',
            '2': 'New York', '3': 'New Jersey',
            '4': 'Pennsylvania', '5': 'Iowa',
            '6': 'Illinois', '7': 'Louisiana',
            '8': 'Colorado', '9': 'California'
        };
        return stateMap[zipFirstDigit] || 'California';
    }

    // 选择州
    async function selectState(stateName) {
        try {
            const stateButton = await waitForElement('#states\\[0\\]\\.state-button');
            simulateClick(stateButton);

            await new Promise(resolve => setTimeout(resolve, 1000));

            const dropdownList = await waitForElement('#dropdown-list');
            const items = dropdownList.querySelectorAll('li[role="option"]');

            for (const item of items) {
                if (item.textContent.trim() === stateName) {
                    simulateClick(item);
                    console.log('已选择州:', stateName);
                    break;
                }
            }

            await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
            console.error('选择州失败:', error);
        }
    }

    // 检查报税状态页面
    async function checkFilingStatusPage() {
        try {
            const filingWizardDiv = document.querySelector('div#filingWizard');
            if (!filingWizardDiv) return false;

            const strongText = filingWizardDiv.querySelector('strong');
            return strongText && strongText.textContent === 'Need help determining your filing status?';
        } catch (error) {
            return false;
        }
    }

    // 处理报税状态页面
    async function handleFilingStatusPage() {
        if (executedSteps.filingStatusPage) {
            console.log('报税状态页面已处理过，跳过...');
            return;
        }

        console.log('开始处理报税状态页面...');

        try {
            // 选择Single选项
            const singleLabel = await waitForElement('label[for="filingStatus_1Radio"]');
            const singleRadio = document.getElementById('filingStatus_1Radio');

            simulateClick(singleLabel);
            if (singleRadio) {
                singleRadio.checked = true;
                singleRadio.dispatchEvent(new Event('change', { bubbles: true }));
            }

            await new Promise(resolve => setTimeout(resolve, 500));

            // 点击Continue按钮
            const continueBtn = await waitForElement('button#btnSubmit[title="Continue"]');
            continueBtn.disabled = false;
            continueBtn.classList.remove('btn-not-ready');
            simulateClick(continueBtn);

            // 标记为已完成
            executedSteps.filingStatusPage = true;
            await saveExecutedSteps();

            await waitForPageTransition();
            console.log('报税状态页面处理完成');

        } catch (error) {
            console.error('处理报税状态页面失败:', error);
        }
    }

    // 检查受抚养人页面
    async function checkDependentsPage() {
        try {
            const pageTitle = document.querySelector('h1#page-title');
            return pageTitle && pageTitle.textContent === 'Dependents or Qualifying Person(s)';
        } catch (error) {
            return false;
        }
    }

    // 处理受抚养人页面
    async function handleDependentsPage() {
        if (executedSteps.dependentsPage) {
            console.log('受抚养人页面已处理过，跳过...');
            return;
        }

        console.log('开始处理受抚养人页面...');

        try {
            // 选择No选项
            const noLabel = await waitForElement('label[for="needToAddDependent_2Radio"]');
            simulateClick(noLabel);

            await new Promise(resolve => setTimeout(resolve, 1000));

            // 点击Continue按钮
            const continueButton = await waitForElement('button#btnSubmit[title="Continue"]');
            simulateClick(continueButton);

            // 标记为已完成
            executedSteps.dependentsPage = true;
            await saveExecutedSteps();

            await waitForPageTransition();
            console.log('受抚养人页面处理完成');

        } catch (error) {
            console.error('处理受抚养人页面失败:', error);
        }
    }

    // 通用的Continue按钮处理
    async function handleGenericContinuePage(pageIdentifier, stepKey) {
        if (executedSteps[stepKey]) {
            console.log(`${pageIdentifier}页面已处理过，跳过...`);
            return;
        }

        console.log(`开始处理${pageIdentifier}页面...`);

        try {
            // 查找Continue按钮的多种可能选择器
            const buttonSelectors = [
                'button#btnSubmit[title="Continue"]',
                'button#registration-continue-submit',
                'button#add-state-btn',
                'a#btn-Save-BasicInfoSummary',
                'a.btn.btn-success'
            ];

            let continueButton = null;
            for (const selector of buttonSelectors) {
                try {
                    continueButton = await waitForElement(selector, 2000);
                    break;
                } catch (error) {
                    // 继续尝试下一个选择器
                }
            }

            if (continueButton) {
                simulateClick(continueButton);

                // 标记为已完成
                executedSteps[stepKey] = true;
                await saveExecutedSteps();

                await waitForPageTransition();
                console.log(`${pageIdentifier}页面处理完成`);
            } else {
                console.log(`未找到${pageIdentifier}页面的Continue按钮`);
            }

        } catch (error) {
            console.error(`处理${pageIdentifier}页面失败:`, error);
        }
    }

    // 添加更多页面检查和处理
    async function identifyAndProcessStep() {
        // 等待页面稳定
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 检查各种页面类型
        if (await checkTaxpayerInfoPage()) {
            await handleTaxpayerInfo();
        } else if (await checkAddressInfoPage()) {
            await handleAddressInfo();
        } else if (await checkConsentPage()) {
            await handleConsent();
        } else if (await checkW2FormPage()) {
            await handleW2Form();
        } else if (await checkFilingStatusPage()) {
            await handleFilingStatusPage();
        } else if (await checkDependentsPage()) {
            await handleDependentsPage();
        } else if (await checkGenericPage('Additional Information', 'h1[data-testid="page-title-h1"]')) {
            await handleGenericContinuePage('附加信息', 'additionalInfo');
        } else if (await checkGenericPage('IRS Identity Protection PIN', 'h1#page-title')) {
            await handleGenericContinuePage('身份保护PIN', 'identityPinPage');
        } else if (await checkGenericPage('Basic Information Summary', 'h1#page-title')) {
            await handleGenericContinuePage('基本信息摘要', 'basicInfoSummary');
        } else if (await checkGenericPage('Let\'s do this! #slayit', 'h1[data-testid="page-title-h1"]')) {
            await handleGenericContinuePage('开始', 'startPage');
        } else {
            console.log('未识别的页面类型，等待页面变化...');
            setupPageChangeObserver();
        }
    }

    // 通用页面检查
    async function checkGenericPage(expectedText, selector) {
        try {
            const element = document.querySelector(selector);
            return element && element.textContent.includes(expectedText);
        } catch (error) {
            return false;
        }
    }

    // 页面加载时自动开始（如果之前已启动）
    chrome.runtime.sendMessage({ action: 'getAutoFillStatus' }, (response) => {
        if (response && response.success && response.isActive) {
            // 获取用户数据并启动
            chrome.storage.local.get(['userData'], (result) => {
                if (result.userData) {
                    startAutoFill(result.userData);
                }
            });
        }
    });

})();
