# 🚀 TaxSlayer 杀手半自动填写助手

这是一个完整的浏览器扩展程序，完全复制了原油猴脚本的所有功能，用于自动化 TaxSlayer 网站的报税流程，**彻底解决了原脚本需要对焦的问题**。

## ✨ 核心优势

- ✅ **完全无需对焦** - 插件在后台运行，不受页面焦点影响，可以在后台自动执行
- ✅ **功能100%还原** - 包含原脚本的所有48个步骤和页面处理逻辑
- ✅ **批量导入支持** - 完全兼容原脚本的Tab分隔数据导入格式
- ✅ **智能页面识别** - 自动识别所有TaxSlayer页面类型并执行相应操作
- ✅ **数据持久化** - 用户数据和执行状态自动保存，支持中断恢复
- ✅ **专业界面** - 与原脚本相同的"杀手半自动"标识和导入界面

## 安装方法

1. 下载或克隆此项目到本地
2. 打开 Chrome 浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `taxslayer-extension`

## 🎯 使用方法

### 方法1：批量导入（推荐，与原脚本完全一致）
1. **准备数据**：按照以下格式准备数据（用Tab键分隔）
   ```
   邮箱	用户名	密码	手机号	手机验证链接	名字	姓氏	社会安全号	出生日期	地址	邮编	雇主EIN	雇主公司	雇主地址	雇主邮编	金额1	金额2	金额3	金额4	金额5	金额6	金额16	金额17	银行名称	路由号码	账号
   ```

2. **导入数据**：
   - 点击浏览器工具栏中的插件图标
   - 在"批量导入数据"区域粘贴数据
   - 点击"导入并开始"按钮

3. **自动执行**：
   - 插件将自动开始填写流程
   - 支持所有48个步骤的自动处理

### 方法2：手动输入
1. **填写表单**：在插件弹窗中手动填写各项信息
2. **保存数据**：点击"保存数据"按钮
3. **开始填写**：访问 TaxSlayer 网站，点击"开始自动填写"

### 控制操作
- ✅ **开始/停止**：随时控制自动填写的启动和停止
- ✅ **数据管理**：保存、加载、清空数据
- ✅ **状态监控**：实时显示执行状态和进度

## 📋 支持的页面（完整48个步骤）

### 注册和验证流程
- ✅ 注册页面（带导入按钮）
- ✅ 验证码页面（自动打开验证链接）
- ✅ 账户创建确认页面

### 基本信息填写
- ✅ 纳税人信息页面（姓名、SSN、出生日期）
- ✅ 地址信息页面（地址、邮编、居民状态）
- ✅ 同意授权页面（双重签名确认）

### 报税信息
- ✅ 报税状态页面（Single选择）
- ✅ 受抚养人页面
- ✅ 身份保护PIN页面
- ✅ 基本信息摘要页面

### W-2表单处理
- ✅ W-2表单页面（完整雇主和工资信息）
- ✅ W-2工资声明页面
- ✅ 州选择（基于邮编自动选择）

### 附加信息
- ✅ 附加信息页面
- ✅ 其他事项页面
- ✅ 平价医疗法案页面
- ✅ 州申报相关页面

### 提交和支付流程
- ✅ 准备提交页面
- ✅ E-file中心（4个步骤）
- ✅ 联邦/州退款方式页面
- ✅ 银行账户信息页面
- ✅ MD直接存款详情页面
- ✅ ID信息页面
- ✅ PIN页面（自动使用SSN后4位）
- ✅ 最后一步页面

### 特殊页面
- ✅ 马里兰州相关页面
- ✅ Protection Plus页面
- ✅ Last Look页面
- ✅ 下载申报表页面

## 技术特点

### 相比油猴脚本的改进

1. **无对焦依赖**：
   - 移除了所有 `focus()` 和 `blur()` 调用
   - 使用事件模拟替代焦点操作
   - 后台运行，不受页面焦点影响

2. **更稳定的执行**：
   - 使用 MutationObserver 监听页面变化
   - 智能重试机制
   - 更好的错误处理

3. **状态管理**：
   - 跨页面状态保持
   - 执行步骤记录
   - 支持中断恢复

## 文件结构

```
taxslayer-extension/
├── manifest.json          # 扩展程序配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 主要自动化逻辑
├── background.js          # 后台服务工作者
├── icons/                 # 图标文件夹
└── README.md             # 说明文档
```

## 注意事项

- 请确保在 TaxSlayer 官方网站使用
- 建议在使用前备份重要数据
- 如遇到问题，可以停止自动填写并手动操作
- 插件仅用于学习和个人使用

## 开发说明

如需修改或扩展功能：

1. 修改 `content.js` 添加新的页面处理逻辑
2. 更新 `popup.html` 和 `popup.js` 修改用户界面
3. 在 `background.js` 中添加后台处理逻辑
4. 重新加载扩展程序测试

## 许可证

本项目仅供学习和个人使用。请遵守相关法律法规和网站使用条款。
